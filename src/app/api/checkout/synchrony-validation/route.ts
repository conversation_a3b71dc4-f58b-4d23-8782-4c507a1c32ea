import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import { backendGetSynchronySession } from '~/lib/backend/checkout/synchrony-validation';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import logger from '~/lib/helpers/logger';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const requestBody = await request.json();

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const userSessionId = extraQueryParams.xUserSessionId;

  if (!userSessionId) {
    logger.error('Invalid authentication');
    return new Response(null, { status: 204 });
  }

  const res = await backendGetSynchronySession(
    {
      cartId,
      input: requestBody,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
