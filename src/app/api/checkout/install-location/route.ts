import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendCreateCartInstallLocation,
  backendDeleteCartInstallLocation,
  backendGetCartInstallLocation,
  backendUpdateCartInstallLocation,
} from '~/lib/backend/checkout/install-location';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function GET(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendGetCartInstallLocation({ cartId }, extraQueryParams);

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendCreateCartInstallLocation(
    {
      cartId,
      input: requestBody,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } else if (res.error.statusCode === 400) {
    const updateRes = await backendUpdateCartInstallLocation(
      {
        cartId,
        input: requestBody,
      },
      extraQueryParams,
    );

    if (updateRes.isSuccess) {
      return new Response(JSON.stringify(updateRes.data), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(
      JSON.stringify({ error: updateRes.error, isSuccess: false }),
      {
        status: updateRes.error.statusCode,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }

  return new Response(JSON.stringify({ error: res.error, isSuccess: false }), {
    status: res.error.statusCode,
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendUpdateCartInstallLocation(
    {
      cartId,
      input: requestBody,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(JSON.stringify({ error: res.error, isSuccess: false }), {
    status: res.error.statusCode,
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function DELETE(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendDeleteCartInstallLocation(
    {
      cartId,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(JSON.stringify({ error: res.error, isSuccess: false }), {
    status: res.error.statusCode,
    headers: { 'Content-Type': 'application/json' },
  });
}
