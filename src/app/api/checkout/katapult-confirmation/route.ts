import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { NextRequest } from 'next/server';

import { COOKIES } from '~/lib/constants/cookies';
import { isProductionDeploy } from '~/lib/utils/deploy';

interface KatapultProps {
  customer_id: string;
  uid: string;
  zibby_id: string;
}

const katapultUrl = isProductionDeploy()
  ? 'https://katapult.com'
  : 'https://sandbox.katapult.com';

export async function GET() {
  redirect('/checkout/katapult-confirmation');
}

export async function POST(request: NextRequest) {
  const currentDate = new Date();
  const expiryDate = new Date(currentDate.setDate(currentDate.getDate() + 1));

  const param: KatapultProps = await request.json();

  if (param) {
    const cookieStore = await cookies();

    // Set the three Katapult cookies
    cookieStore.set(COOKIES.KATAPULT_ZIBBY_ID_COOKIE, param.zibby_id, {
      expires: expiryDate,
      path: '/',
      sameSite: 'none',
      secure: true,
    });

    cookieStore.set(COOKIES.KATAPULT_CUSTOMER_COOKIE, param.customer_id, {
      expires: expiryDate,
      path: '/',
      sameSite: 'none',
      secure: true,
    });

    cookieStore.set(COOKIES.KATAPULT_UID_COOKIE, param.uid, {
      expires: expiryDate,
      path: '/',
      sameSite: 'none',
      secure: true,
    });

    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Origin': katapultUrl,
        'Content-Type': 'application/json',
      },
    });
  }

  return new Response(null, { status: 400 });
}
