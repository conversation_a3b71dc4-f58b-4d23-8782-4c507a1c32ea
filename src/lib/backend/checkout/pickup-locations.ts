import { PickupLocationsResponse } from '~/data/models/SiteShippingLocation';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetPickupLocations(
  {
    query,
  }: {
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<PickupLocationsResponse>({
    endpoint: '/v2/site/pickup-locations',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    query,
  });
}
