import { WarehousePickupLocationsResponse } from '~/data/models/SiteWarehouseShippingLocation';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetWarehousePickupLocations(
  {
    query,
  }: {
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<WarehousePickupLocationsResponse>({
    endpoint: '/v2/site/warehouse-pickup-locations',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    query,
  });
}
