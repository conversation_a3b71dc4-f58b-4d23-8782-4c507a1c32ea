import { SynchronyValidationRequest } from '~/data/models/SynchronyValidationRequest';
import { SynchronyValidationResponse } from '~/data/models/SynchronyValidationResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetSynchronySession(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SynchronyValidationRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SynchronyValidationResponse,
    SynchronyValidationRequest
  >({
    endpoint: '/v2/site/payment/session/{cartId}',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
  });
}
