import { PickupLocationsResponse } from '~/data/models/SiteShippingLocation';
import { fetchWithErrorHandling } from '~/lib/fetch';

export async function apiGetPickupLocations({
  signal,
  includeUserRegion,
  includeUserZip,
  limit,
}: {
  includeUserRegion: boolean;
  includeUserZip: boolean;
  limit: string;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<PickupLocationsResponse>({
    endpoint: '/checkout/pickup-locations',
    includeUserRegion,
    includeUserSSOUid: true,
    includeUserZip,
    method: 'get',
    query: {
      limit,
    },
    signal,
  });
}
