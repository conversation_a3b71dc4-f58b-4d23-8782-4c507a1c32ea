import { SiteInstallLocationRequest } from '~/data/models/SiteInstallLocationRequest';
import { SiteInstallLocationApiResponse } from '~/data/models/SiteInstallLocationResponse';
import { fetchWithErrorHandling } from '~/lib/fetch';

export async function apiGetSiteInstallLocation({
  query,
  signal,
}: {
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<SiteInstallLocationApiResponse, null>({
    endpoint: '/checkout/install-location',
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    method: 'get',
    query,
    signal,
  });
}

export async function apiCreateSiteInstallLocation({
  input,
  query,
  signal,
}: {
  input: SiteInstallLocationRequest;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/checkout/install-location',
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'post',
    query,
    signal,
  });
}

export async function apiUpdateSiteCartInstallLocation({
  input,
  query,
  signal,
}: {
  input: SiteInstallLocationRequest;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/checkout/install-location',
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'put',
    query,
    signal,
  });
}

export async function apiDeleteSiteInstallLocation({
  query,
  signal,
}: {
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/checkout/install-location',
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    method: 'delete',
    query,
    signal,
  });
}
